.Features {
  /* border: 1px solid red; */
  /* overflow: hidden; */
  margin: 2rem auto;
}

.Features h2 {
  margin: 1rem auto;
  justify-content: center;
  text-align: center;
  color: #89c4ee;
}

.Features-Content {
  padding: 1rem;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
}

.Features-Content-Card {
  position: relative;
  border-radius: 5%;
  background-color: whitesmoke;
  width: 90%;
  margin: 1rem 0;
  padding: 1.5rem;
  overflow: hidden;
  transition: transform 0.3s ease;
}

.Features-Content-Card:hover {
  transform: translateY(-10px);
}

.Features-Content-Card::before {
  content: '';
  position: absolute;
  bottom: -100%;
  left: 0;
  width: 100%;
  height: 100%;
  background: #89c4ee;
  transition: bottom 0.3s ease;
  z-index: -1;
}

.Features-Content-Card:hover::before {
  bottom: 0;
}

.Features-Content-Card .card-content {
  position: relative;
  z-index: 1;
  color: #333;
}

.Features-Content .Features-Content-Card h5 {
  text-align: center;
  justify-content: center;
}
@media (max-width:1024px){
  .Features-Content{
    display: grid;
    grid-template-columns: 1fr 1fr;
  }
}
@media (max-width:480px) {
  .Features-Content {
    padding: 1rem;
    display: block;
    justify-content: center;
    align-items: center;
    text-align: center;
  }
  .Features-Content-Card {
    width:100%;
    border-radius: 1%;
  }
}