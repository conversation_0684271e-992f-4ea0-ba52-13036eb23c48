.Candidatesignup {
    /* border: 1px solid red; */
    margin: 0rem;
    height: 50%;
    padding: 0rem;
    /* display: inline-flex;
     */
  }

.Candidatesignup .container{
    /* border: 1px solid green; */
    border-radius: 0;
    padding: 0rem;
    margin: 0rem;
    width: 100%;
    height: 50vh;
}
.Candidatesignup .FormTitle{
    width: 100%;
    display: block;
    align-items: center;
    justify-content: center;
    text-align: center;
}
.Candidatesignup .container .signup-form{
    /* border: 1px solid blue; */
    margin: 0;
    padding: 0rem;
    width: 100%;
    /* display:flex; */
    height: 50vh;
}
.Candidatesignup .signup-content{
    padding: 0rem;
}
.Candidatesignup .container .signup-form .register-form{
    /* border: 1px solid orange; */
    width: 100%;
    margin: 0rem;
    padding: 0rem;
    height: 50vh;
    justify-content: center;
    align-items: center;
    text-align: center;
    overflow-y: scroll;
}

.Candidatesignup .container .signup-form .register-form .form-group{
    /* border: 1px solid black; */
    display: inline-flex;
    margin: 0.6rem 0.5rem;
    /* padding: 0rem 0.5rem; */
    width: 40%;
}