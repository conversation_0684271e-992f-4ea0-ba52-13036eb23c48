# Uploads Directory

This directory is used to store uploaded files for the Online Voting System.

## Structure

- `/profiles` - User profile pictures
- `/idProofs` - User ID proof documents
- `/candidates` - Candidate profile pictures
- `/symbols` - Party symbols for candidates

## Notes

- This directory is created automatically when you run `npm run init-uploads` or `npm run setup`
- The directory structure is maintained in Git, but the actual uploaded files are ignored (see .gitignore)
- Default placeholder images will be used if uploads are missing
