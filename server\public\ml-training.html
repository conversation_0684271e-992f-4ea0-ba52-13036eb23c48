<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ML Document Classification Training Interface</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        
        .tab {
            flex: 1;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }
        
        .tab:hover {
            background: #e9ecef;
        }
        
        .tab.active {
            background: white;
            border-bottom-color: #3498db;
            color: #3498db;
        }
        
        .tab-content {
            display: none;
            padding: 30px;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }
        
        .card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
        }
        
        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #3498db;
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #27ae60;
            color: white;
        }
        
        .btn-success:hover {
            background: #229954;
            transform: translateY(-2px);
        }
        
        .btn-warning {
            background: #f39c12;
            color: white;
        }
        
        .btn-warning:hover {
            background: #e67e22;
            transform: translateY(-2px);
        }
        
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c0392b;
            transform: translateY(-2px);
        }
        
        .progress-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #dee2e6;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .status-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border: 2px solid #dee2e6;
            transition: transform 0.3s ease;
        }
        
        .status-card:hover {
            transform: translateY(-5px);
        }
        
        .status-value {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }
        
        .status-label {
            color: #6c757d;
            font-size: 0.9em;
        }
        
        .log-container {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 10px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            height: 300px;
            overflow-y: auto;
            margin: 20px 0;
        }
        
        .file-upload-area {
            border: 3px dashed #3498db;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 20px 0;
        }
        
        .file-upload-area:hover {
            background: #f8f9fa;
            border-color: #2980b9;
        }
        
        .file-upload-area.dragover {
            background: #e3f2fd;
            border-color: #1976d2;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .hidden {
            display: none;
        }
        
        .model-card {
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            transition: all 0.3s ease;
        }
        
        .model-card:hover {
            border-color: #3498db;
            transform: translateY(-2px);
        }
        
        .model-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        
        .info-item {
            text-align: center;
        }
        
        .info-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .info-label {
            color: #6c757d;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 ML Document Classification Training</h1>
            <p>Train, Upload, and Manage Machine Learning Models</p>
        </div>
        
        <div class="tabs">
            <div class="tab active" onclick="showTab('upload')">📤 Upload Data</div>
            <div class="tab" onclick="showTab('train')">🎯 Train Model</div>
            <div class="tab" onclick="showTab('test')">🧪 Test Model</div>
            <div class="tab" onclick="showTab('models')">📊 Manage Models</div>
            <div class="tab" onclick="showTab('results')">📈 View Results</div>
        </div>
        
        <!-- Upload Data Tab -->
        <div id="upload" class="tab-content active">
            <div class="card">
                <h3>📤 Upload Training Data</h3>
                <div class="form-group">
                    <label>Document Type:</label>
                    <select id="documentType" class="form-control">
                        <option value="aadhar">Aadhar Card</option>
                        <option value="voter_id">Voter ID</option>
                        <option value="driving_license">Driving License</option>
                        <option value="passport">Passport</option>
                        <option value="pan_card">PAN Card</option>
                        <option value="bank_statement">Bank Statement</option>
                        <option value="utility_bill">Utility Bill</option>
                        <option value="unknown">Unknown</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>Dataset Type:</label>
                    <select id="datasetType" class="form-control">
                        <option value="training">Training (60%)</option>
                        <option value="validation">Validation (20%)</option>
                        <option value="test">Test (20%)</option>
                    </select>
                </div>
                
                <div class="file-upload-area" id="fileUploadArea">
                    <h3>📁 Drop files here or click to browse</h3>
                    <p>Supported formats: JPG, PNG, GIF, BMP</p>
                    <p>Maximum file size: 10MB per image</p>
                    <input type="file" id="fileInput" multiple accept="image/*" style="display: none;">
                </div>
                
                <div id="fileList" class="hidden">
                    <h4>Selected Files:</h4>
                    <div id="selectedFiles"></div>
                </div>
                
                <button class="btn btn-primary" onclick="uploadFiles()">📤 Upload Training Data</button>
                <button class="btn btn-warning" onclick="clearFiles()">🗑️ Clear Selection</button>
            </div>
            
            <div class="card">
                <h3>📊 Current Dataset Statistics</h3>
                <div id="datasetStats" class="status-grid">
                    <div class="status-card">
                        <div class="status-value" id="totalSamples">0</div>
                        <div class="status-label">Total Samples</div>
                    </div>
                    <div class="status-card">
                        <div class="status-value" id="trainingSamples">0</div>
                        <div class="status-label">Training</div>
                    </div>
                    <div class="status-card">
                        <div class="status-value" id="validationSamples">0</div>
                        <div class="status-label">Validation</div>
                    </div>
                    <div class="status-card">
                        <div class="status-value" id="testSamples">0</div>
                        <div class="status-label">Test</div>
                    </div>
                </div>
                <button class="btn btn-primary" onclick="refreshStats()">🔄 Refresh Statistics</button>
            </div>
        </div>

        <!-- Train Model Tab -->
        <div id="train" class="tab-content">
            <div class="card">
                <h3>⚙️ Training Configuration</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                    <div class="form-group">
                        <label>Epochs:</label>
                        <input type="number" id="epochs" class="form-control" value="50" min="1" max="200">
                    </div>
                    <div class="form-group">
                        <label>Batch Size:</label>
                        <input type="number" id="batchSize" class="form-control" value="32" min="8" max="128">
                    </div>
                    <div class="form-group">
                        <label>Learning Rate:</label>
                        <input type="number" id="learningRate" class="form-control" value="0.001" step="0.0001" min="0.0001" max="0.1">
                    </div>
                    <div class="form-group">
                        <label>Validation Split:</label>
                        <input type="number" id="validationSplit" class="form-control" value="0.2" step="0.1" min="0.1" max="0.5">
                    </div>
                </div>

                <button class="btn btn-success" onclick="initializeTraining()">🔧 Initialize Training Session</button>
                <button class="btn btn-primary" onclick="startTraining()">🚀 Start Training</button>
                <button class="btn btn-danger" onclick="stopTraining()">⏹️ Stop Training</button>
            </div>

            <div class="card">
                <h3>📊 Training Progress</h3>
                <div id="trainingStatus" class="alert alert-info">
                    Ready to start training. Initialize a session first.
                </div>

                <div class="progress-container">
                    <div class="progress-bar">
                        <div id="trainingProgress" class="progress-fill"></div>
                    </div>
                    <div style="text-align: center; margin-top: 10px;">
                        <span id="progressText">0% Complete</span>
                    </div>
                </div>

                <div class="status-grid">
                    <div class="status-card">
                        <div class="status-value" id="currentEpoch">0</div>
                        <div class="status-label">Current Epoch</div>
                    </div>
                    <div class="status-card">
                        <div class="status-value" id="currentLoss">0.00</div>
                        <div class="status-label">Loss</div>
                    </div>
                    <div class="status-card">
                        <div class="status-value" id="currentAccuracy">0.00%</div>
                        <div class="status-label">Accuracy</div>
                    </div>
                    <div class="status-card">
                        <div class="status-value" id="trainingTime">0s</div>
                        <div class="status-label">Training Time</div>
                    </div>
                </div>
            </div>

            <div class="card">
                <h3>📝 Training Logs</h3>
                <div id="trainingLogs" class="log-container">
                    Training logs will appear here...
                </div>
                <button class="btn btn-warning" onclick="clearLogs()">🗑️ Clear Logs</button>
            </div>
        </div>

        <!-- Test Model Tab -->
        <div id="test" class="tab-content">
            <div class="card">
                <h3>🧪 Test Trained Model</h3>
                <div class="file-upload-area" id="testFileUploadArea">
                    <h3>📁 Drop test image here or click to browse</h3>
                    <p>Upload a document image to test classification</p>
                    <input type="file" id="testFileInput" accept="image/*" style="display: none;">
                </div>

                <button class="btn btn-primary" onclick="testModel()">🔍 Classify Document</button>
                <button class="btn btn-warning" onclick="clearTestResults()">🗑️ Clear Results</button>
            </div>

            <div id="testResults" class="card hidden">
                <h3>📋 Classification Results</h3>
                <div id="testResultsContent"></div>
            </div>

            <div class="card">
                <h3>📊 Model Performance</h3>
                <div id="modelPerformance" class="status-grid">
                    <div class="status-card">
                        <div class="status-value" id="modelAccuracy">N/A</div>
                        <div class="status-label">Model Accuracy</div>
                    </div>
                    <div class="status-card">
                        <div class="status-value" id="totalTests">0</div>
                        <div class="status-label">Total Tests</div>
                    </div>
                    <div class="status-card">
                        <div class="status-value" id="correctPredictions">0</div>
                        <div class="status-label">Correct</div>
                    </div>
                    <div class="status-card">
                        <div class="status-value" id="avgConfidence">0%</div>
                        <div class="status-label">Avg Confidence</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Manage Models Tab -->
        <div id="models" class="tab-content">
            <div class="card">
                <h3>📊 Available Models</h3>
                <div id="modelsList">
                    <div class="model-card">
                        <h4>Document Classification Model</h4>
                        <div class="model-info">
                            <div class="info-item">
                                <div class="info-value" id="modelVersion">v1.0</div>
                                <div class="info-label">Version</div>
                            </div>
                            <div class="info-item">
                                <div class="info-value" id="modelSize">25.4 MB</div>
                                <div class="info-label">Size</div>
                            </div>
                            <div class="info-item">
                                <div class="info-value" id="modelAccuracyDisplay">91.5%</div>
                                <div class="info-label">Accuracy</div>
                            </div>
                            <div class="info-item">
                                <div class="info-value" id="lastTrained">Today</div>
                                <div class="info-label">Last Trained</div>
                            </div>
                        </div>
                        <button class="btn btn-primary" onclick="downloadModel()">💾 Download Model</button>
                        <button class="btn btn-success" onclick="loadModel()">📂 Load Model</button>
                        <button class="btn btn-danger" onclick="deleteModel()">🗑️ Delete Model</button>
                    </div>
                </div>
            </div>

            <div class="card">
                <h3>📤 Upload Pre-trained Model</h3>
                <div class="file-upload-area" id="modelUploadArea">
                    <h3>📁 Drop model files here or click to browse</h3>
                    <p>Upload model.json and weight files</p>
                    <input type="file" id="modelFileInput" multiple accept=".json,.bin" style="display: none;">
                </div>
                <button class="btn btn-success" onclick="uploadModel()">📤 Upload Model</button>
            </div>

            <div class="card">
                <h3>🔄 Model Operations</h3>
                <button class="btn btn-primary" onclick="backupModel()">💾 Backup Current Model</button>
                <button class="btn btn-warning" onclick="restoreModel()">🔄 Restore from Backup</button>
                <button class="btn btn-success" onclick="exportModel()">📦 Export Model</button>
                <button class="btn btn-danger" onclick="resetModel()">🔄 Reset to Default</button>
            </div>
        </div>

        <!-- View Results Tab -->
        <div id="results" class="tab-content">
            <div class="card">
                <h3>📈 Training History</h3>
                <div id="trainingHistory">
                    <div class="status-grid" id="historyStats">
                        <div class="status-card">
                            <div class="status-value" id="totalSessions">0</div>
                            <div class="status-label">Training Sessions</div>
                        </div>
                        <div class="status-card">
                            <div class="status-value" id="bestAccuracy">0%</div>
                            <div class="status-label">Best Accuracy</div>
                        </div>
                        <div class="status-card">
                            <div class="status-value" id="totalTrainingTime">0h</div>
                            <div class="status-label">Total Training Time</div>
                        </div>
                        <div class="status-card">
                            <div class="status-value" id="lastSession">Never</div>
                            <div class="status-label">Last Session</div>
                        </div>
                    </div>
                </div>
                <button class="btn btn-primary" onclick="refreshHistory()">🔄 Refresh History</button>
                <button class="btn btn-warning" onclick="exportHistory()">📊 Export History</button>
            </div>

            <div class="card">
                <h3>📊 Performance Analytics</h3>
                <div id="performanceCharts">
                    <canvas id="accuracyChart" width="400" height="200"></canvas>
                    <canvas id="lossChart" width="400" height="200"></canvas>
                </div>
            </div>

            <div class="card">
                <h3>📋 Recent Classifications</h3>
                <div id="recentClassifications">
                    <div class="log-container" id="classificationsLog">
                        Recent classification results will appear here...
                    </div>
                </div>
                <button class="btn btn-primary" onclick="refreshClassifications()">🔄 Refresh</button>
                <button class="btn btn-warning" onclick="exportClassifications()">📊 Export Results</button>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let selectedFiles = [];
        let currentTrainingSession = null;
        let trainingInterval = null;
        let testFile = null;
        let modelFiles = [];

        // Tab management
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));

            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // Show selected tab content
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked tab
            event.target.classList.add('active');

            // Load data for specific tabs
            if (tabName === 'upload') {
                refreshStats();
            } else if (tabName === 'results') {
                refreshHistory();
                refreshClassifications();
            }
        }

        // File upload handling
        document.addEventListener('DOMContentLoaded', function() {
            setupFileUpload();
            setupTestFileUpload();
            setupModelUpload();
            refreshStats();
        });

        function setupFileUpload() {
            const fileUploadArea = document.getElementById('fileUploadArea');
            const fileInput = document.getElementById('fileInput');

            fileUploadArea.addEventListener('click', () => fileInput.click());
            fileUploadArea.addEventListener('dragover', handleDragOver);
            fileUploadArea.addEventListener('drop', handleFileDrop);
            fileInput.addEventListener('change', handleFileSelect);
        }

        function setupTestFileUpload() {
            const testFileUploadArea = document.getElementById('testFileUploadArea');
            const testFileInput = document.getElementById('testFileInput');

            testFileUploadArea.addEventListener('click', () => testFileInput.click());
            testFileUploadArea.addEventListener('dragover', handleDragOver);
            testFileUploadArea.addEventListener('drop', handleTestFileDrop);
            testFileInput.addEventListener('change', handleTestFileSelect);
        }

        function setupModelUpload() {
            const modelUploadArea = document.getElementById('modelUploadArea');
            const modelFileInput = document.getElementById('modelFileInput');

            modelUploadArea.addEventListener('click', () => modelFileInput.click());
            modelUploadArea.addEventListener('dragover', handleDragOver);
            modelUploadArea.addEventListener('drop', handleModelFileDrop);
            modelFileInput.addEventListener('change', handleModelFileSelect);
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.target.classList.add('dragover');
        }

        function handleFileDrop(e) {
            e.preventDefault();
            e.target.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            handleFiles(files);
        }

        function handleFileSelect(e) {
            const files = Array.from(e.target.files);
            handleFiles(files);
        }

        function handleFiles(files) {
            selectedFiles = files.filter(file => file.type.startsWith('image/'));
            displaySelectedFiles();
        }

        function handleTestFileDrop(e) {
            e.preventDefault();
            e.target.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            if (files.length > 0 && files[0].type.startsWith('image/')) {
                testFile = files[0];
                displayTestFile();
            }
        }

        function handleTestFileSelect(e) {
            if (e.target.files.length > 0) {
                testFile = e.target.files[0];
                displayTestFile();
            }
        }

        function handleModelFileDrop(e) {
            e.preventDefault();
            e.target.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            modelFiles = files.filter(file =>
                file.name.endsWith('.json') || file.name.endsWith('.bin')
            );
            displayModelFiles();
        }

        function handleModelFileSelect(e) {
            modelFiles = Array.from(e.target.files);
            displayModelFiles();
        }

        function displaySelectedFiles() {
            const fileList = document.getElementById('fileList');
            const selectedFilesDiv = document.getElementById('selectedFiles');

            if (selectedFiles.length > 0) {
                fileList.classList.remove('hidden');
                selectedFilesDiv.innerHTML = selectedFiles.map(file =>
                    `<div style="padding: 5px; border: 1px solid #ddd; margin: 5px 0; border-radius: 5px;">
                        📄 ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)
                    </div>`
                ).join('');
            } else {
                fileList.classList.add('hidden');
            }
        }

        function displayTestFile() {
            const testFileUploadArea = document.getElementById('testFileUploadArea');
            if (testFile) {
                testFileUploadArea.innerHTML = `
                    <h3>✅ File Selected</h3>
                    <p>📄 ${testFile.name}</p>
                    <p>Size: ${(testFile.size / 1024 / 1024).toFixed(2)} MB</p>
                `;
            }
        }

        function displayModelFiles() {
            const modelUploadArea = document.getElementById('modelUploadArea');
            if (modelFiles.length > 0) {
                modelUploadArea.innerHTML = `
                    <h3>✅ ${modelFiles.length} Files Selected</h3>
                    ${modelFiles.map(file => `<p>📄 ${file.name}</p>`).join('')}
                `;
            }
        }

        function clearFiles() {
            selectedFiles = [];
            document.getElementById('fileInput').value = '';
            document.getElementById('fileList').classList.add('hidden');
        }

        // API Functions
        async function uploadFiles() {
            if (selectedFiles.length === 0) {
                showAlert('Please select files to upload', 'error');
                return;
            }

            const documentType = document.getElementById('documentType').value;
            const datasetType = document.getElementById('datasetType').value;

            const formData = new FormData();
            selectedFiles.forEach(file => {
                formData.append('samples', file);
            });
            formData.append('documentType', documentType);
            formData.append('datasetType', datasetType);

            try {
                showAlert('Uploading files...', 'info');
                const response = await fetch('/api/ml-documents/training/samples/upload', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                if (result.success) {
                    showAlert(`Successfully uploaded ${selectedFiles.length} files`, 'success');
                    clearFiles();
                    refreshStats();
                } else {
                    showAlert(`Upload failed: ${result.error}`, 'error');
                }
            } catch (error) {
                showAlert(`Upload error: ${error.message}`, 'error');
            }
        }

        async function refreshStats() {
            try {
                const response = await fetch('/api/ml-documents/training/stats');
                const stats = await response.json();

                if (stats.success) {
                    document.getElementById('totalSamples').textContent = stats.data.totalSamples || 0;
                    document.getElementById('trainingSamples').textContent = stats.data.training || 0;
                    document.getElementById('validationSamples').textContent = stats.data.validation || 0;
                    document.getElementById('testSamples').textContent = stats.data.test || 0;
                }
            } catch (error) {
                console.error('Failed to refresh stats:', error);
            }
        }

        async function initializeTraining() {
            const config = {
                epochs: parseInt(document.getElementById('epochs').value),
                batchSize: parseInt(document.getElementById('batchSize').value),
                learningRate: parseFloat(document.getElementById('learningRate').value),
                validationSplit: parseFloat(document.getElementById('validationSplit').value)
            };

            try {
                const response = await fetch('/api/ml-documents/training/session/init', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ config })
                });

                const result = await response.json();
                if (result.success) {
                    currentTrainingSession = result.sessionId;
                    showAlert('Training session initialized successfully', 'success');
                    updateTrainingStatus('Session initialized. Ready to start training.');
                    addTrainingLog('✅ Training session initialized: ' + result.sessionId);
                } else {
                    showAlert(`Initialization failed: ${result.error}`, 'error');
                }
            } catch (error) {
                showAlert(`Initialization error: ${error.message}`, 'error');
            }
        }

        async function startTraining() {
            if (!currentTrainingSession) {
                showAlert('Please initialize a training session first', 'error');
                return;
            }

            try {
                const response = await fetch('/api/ml-documents/training/start', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ sessionId: currentTrainingSession })
                });

                const result = await response.json();
                if (result.success) {
                    showAlert('Training started successfully', 'success');
                    updateTrainingStatus('Training in progress...');
                    addTrainingLog('🚀 Training started');
                    startTrainingMonitor();
                } else {
                    showAlert(`Training failed: ${result.error}`, 'error');
                }
            } catch (error) {
                showAlert(`Training error: ${error.message}`, 'error');
            }
        }

        function startTrainingMonitor() {
            trainingInterval = setInterval(async () => {
                try {
                    const response = await fetch('/api/ml-documents/training/session/stats');
                    const stats = await response.json();

                    if (stats.success && stats.data) {
                        updateTrainingProgress(stats.data);

                        if (stats.data.status === 'completed') {
                            stopTrainingMonitor();
                            showAlert('Training completed successfully!', 'success');
                            updateTrainingStatus('Training completed successfully!');
                        } else if (stats.data.status === 'failed') {
                            stopTrainingMonitor();
                            showAlert('Training failed', 'error');
                            updateTrainingStatus('Training failed');
                        }
                    }
                } catch (error) {
                    console.error('Failed to get training stats:', error);
                }
            }, 2000);
        }

        function stopTrainingMonitor() {
            if (trainingInterval) {
                clearInterval(trainingInterval);
                trainingInterval = null;
            }
        }

        function stopTraining() {
            stopTrainingMonitor();
            showAlert('Training stopped', 'info');
            updateTrainingStatus('Training stopped by user');
        }

        function updateTrainingProgress(stats) {
            const progress = (stats.currentEpoch / stats.totalEpochs) * 100;
            document.getElementById('trainingProgress').style.width = progress + '%';
            document.getElementById('progressText').textContent = `${progress.toFixed(1)}% Complete`;

            document.getElementById('currentEpoch').textContent = stats.currentEpoch || 0;
            document.getElementById('currentLoss').textContent = (stats.currentLoss || 0).toFixed(4);
            document.getElementById('currentAccuracy').textContent = ((stats.currentAccuracy || 0) * 100).toFixed(2) + '%';
            document.getElementById('trainingTime').textContent = Math.round((stats.trainingTime || 0) / 1000) + 's';

            if (stats.currentEpoch) {
                addTrainingLog(`Epoch ${stats.currentEpoch}: Loss=${(stats.currentLoss || 0).toFixed(4)}, Accuracy=${((stats.currentAccuracy || 0) * 100).toFixed(2)}%`);
            }
        }

        function updateTrainingStatus(message) {
            document.getElementById('trainingStatus').textContent = message;
        }

        function addTrainingLog(message) {
            const logsContainer = document.getElementById('trainingLogs');
            const timestamp = new Date().toLocaleTimeString();
            logsContainer.innerHTML += `[${timestamp}] ${message}\n`;
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('trainingLogs').innerHTML = 'Training logs cleared...\n';
        }

        // Testing functions
        async function testModel() {
            if (!testFile) {
                showAlert('Please select a test image first', 'error');
                return;
            }

            const formData = new FormData();
            formData.append('document', testFile);

            try {
                showAlert('Classifying document...', 'info');
                const response = await fetch('/api/ml-documents/classify', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                if (result.success) {
                    displayTestResults(result);
                    showAlert('Classification completed', 'success');
                } else {
                    showAlert(`Classification failed: ${result.error}`, 'error');
                }
            } catch (error) {
                showAlert(`Classification error: ${error.message}`, 'error');
            }
        }

        function displayTestResults(result) {
            const testResults = document.getElementById('testResults');
            const testResultsContent = document.getElementById('testResultsContent');

            testResults.classList.remove('hidden');

            const probabilities = Object.entries(result.probabilities || {})
                .sort(([,a], [,b]) => b - a)
                .map(([type, prob]) => `
                    <div style="display: flex; justify-content: space-between; padding: 5px; border-bottom: 1px solid #eee;">
                        <span>${type.replace('_', ' ').toUpperCase()}</span>
                        <span>${(prob * 100).toFixed(2)}%</span>
                    </div>
                `).join('');

            testResultsContent.innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px;">
                    <div class="info-item">
                        <div class="info-value">${result.documentType.replace('_', ' ').toUpperCase()}</div>
                        <div class="info-label">Predicted Type</div>
                    </div>
                    <div class="info-item">
                        <div class="info-value">${(result.confidence * 100).toFixed(2)}%</div>
                        <div class="info-label">Confidence</div>
                    </div>
                    <div class="info-item">
                        <div class="info-value">${result.processingTime || 'N/A'}ms</div>
                        <div class="info-label">Processing Time</div>
                    </div>
                    <div class="info-item">
                        <div class="info-value">${result.isAuthentic ? 'YES' : 'NO'}</div>
                        <div class="info-label">Authentic</div>
                    </div>
                </div>
                <h4>All Probabilities:</h4>
                <div style="border: 1px solid #ddd; border-radius: 5px; padding: 10px;">
                    ${probabilities}
                </div>
            `;
        }

        function clearTestResults() {
            document.getElementById('testResults').classList.add('hidden');
            testFile = null;
            document.getElementById('testFileUploadArea').innerHTML = `
                <h3>📁 Drop test image here or click to browse</h3>
                <p>Upload a document image to test classification</p>
            `;
        }

        // Model management functions
        async function downloadModel() {
            try {
                const response = await fetch('/api/ml-documents/model/download');
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'document-classification-model.zip';
                a.click();
                showAlert('Model download started', 'success');
            } catch (error) {
                showAlert(`Download failed: ${error.message}`, 'error');
            }
        }

        async function uploadModel() {
            if (modelFiles.length === 0) {
                showAlert('Please select model files to upload', 'error');
                return;
            }

            const formData = new FormData();
            modelFiles.forEach(file => {
                formData.append('modelFiles', file);
            });

            try {
                showAlert('Uploading model...', 'info');
                const response = await fetch('/api/ml-documents/model/upload', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                if (result.success) {
                    showAlert('Model uploaded successfully', 'success');
                    modelFiles = [];
                    document.getElementById('modelUploadArea').innerHTML = `
                        <h3>📁 Drop model files here or click to browse</h3>
                        <p>Upload model.json and weight files</p>
                    `;
                } else {
                    showAlert(`Upload failed: ${result.error}`, 'error');
                }
            } catch (error) {
                showAlert(`Upload error: ${error.message}`, 'error');
            }
        }

        async function loadModel() {
            try {
                const response = await fetch('/api/ml-documents/model/load', { method: 'POST' });
                const result = await response.json();
                if (result.success) {
                    showAlert('Model loaded successfully', 'success');
                } else {
                    showAlert(`Load failed: ${result.error}`, 'error');
                }
            } catch (error) {
                showAlert(`Load error: ${error.message}`, 'error');
            }
        }

        async function deleteModel() {
            if (confirm('Are you sure you want to delete the current model?')) {
                try {
                    const response = await fetch('/api/ml-documents/model/delete', { method: 'DELETE' });
                    const result = await response.json();
                    if (result.success) {
                        showAlert('Model deleted successfully', 'success');
                    } else {
                        showAlert(`Delete failed: ${result.error}`, 'error');
                    }
                } catch (error) {
                    showAlert(`Delete error: ${error.message}`, 'error');
                }
            }
        }

        async function backupModel() {
            try {
                const response = await fetch('/api/ml-documents/model/backup', { method: 'POST' });
                const result = await response.json();
                if (result.success) {
                    showAlert('Model backed up successfully', 'success');
                } else {
                    showAlert(`Backup failed: ${result.error}`, 'error');
                }
            } catch (error) {
                showAlert(`Backup error: ${error.message}`, 'error');
            }
        }

        async function restoreModel() {
            try {
                const response = await fetch('/api/ml-documents/model/restore', { method: 'POST' });
                const result = await response.json();
                if (result.success) {
                    showAlert('Model restored successfully', 'success');
                } else {
                    showAlert(`Restore failed: ${result.error}`, 'error');
                }
            } catch (error) {
                showAlert(`Restore error: ${error.message}`, 'error');
            }
        }

        async function exportModel() {
            try {
                const response = await fetch('/api/ml-documents/model/export');
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'exported-model.zip';
                a.click();
                showAlert('Model export started', 'success');
            } catch (error) {
                showAlert(`Export failed: ${error.message}`, 'error');
            }
        }

        async function resetModel() {
            if (confirm('Are you sure you want to reset to the default model? This will delete all training progress.')) {
                try {
                    const response = await fetch('/api/ml-documents/model/reset', { method: 'POST' });
                    const result = await response.json();
                    if (result.success) {
                        showAlert('Model reset successfully', 'success');
                    } else {
                        showAlert(`Reset failed: ${result.error}`, 'error');
                    }
                } catch (error) {
                    showAlert(`Reset error: ${error.message}`, 'error');
                }
            }
        }

        // Results and history functions
        async function refreshHistory() {
            try {
                const response = await fetch('/api/ml-documents/training/history');
                const history = await response.json();

                if (history.success && history.data) {
                    const sessions = history.data;
                    document.getElementById('totalSessions').textContent = sessions.length;

                    if (sessions.length > 0) {
                        const bestSession = sessions.reduce((best, current) =>
                            (current.finalAccuracy || 0) > (best.finalAccuracy || 0) ? current : best
                        );
                        document.getElementById('bestAccuracy').textContent =
                            ((bestSession.finalAccuracy || 0) * 100).toFixed(2) + '%';

                        const totalTime = sessions.reduce((total, session) =>
                            total + (session.trainingTime || 0), 0
                        );
                        document.getElementById('totalTrainingTime').textContent =
                            Math.round(totalTime / 3600000) + 'h';

                        const lastSession = sessions[sessions.length - 1];
                        document.getElementById('lastSession').textContent =
                            new Date(lastSession.startTime).toLocaleDateString();
                    }
                }
            } catch (error) {
                console.error('Failed to refresh history:', error);
            }
        }

        async function refreshClassifications() {
            try {
                const response = await fetch('/api/ml-documents/classifications/recent');
                const classifications = await response.json();

                if (classifications.success && classifications.data) {
                    const log = document.getElementById('classificationsLog');
                    log.innerHTML = classifications.data.map(item =>
                        `[${new Date(item.createdAt).toLocaleString()}] ${item.classificationResult.documentType} (${(item.classificationResult.confidence * 100).toFixed(2)}%)`
                    ).join('\n');
                }
            } catch (error) {
                console.error('Failed to refresh classifications:', error);
            }
        }

        async function exportHistory() {
            try {
                const response = await fetch('/api/ml-documents/training/history/export');
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'training-history.json';
                a.click();
                showAlert('History export started', 'success');
            } catch (error) {
                showAlert(`Export failed: ${error.message}`, 'error');
            }
        }

        async function exportClassifications() {
            try {
                const response = await fetch('/api/ml-documents/classifications/export');
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'classifications.csv';
                a.click();
                showAlert('Classifications export started', 'success');
            } catch (error) {
                showAlert(`Export failed: ${error.message}`, 'error');
            }
        }

        // Utility functions
        function showAlert(message, type) {
            // Remove existing alerts
            const existingAlerts = document.querySelectorAll('.alert');
            existingAlerts.forEach(alert => alert.remove());

            // Create new alert
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;

            // Insert at the top of the active tab content
            const activeTab = document.querySelector('.tab-content.active');
            activeTab.insertBefore(alert, activeTab.firstChild);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 5000);
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            refreshStats();
            refreshHistory();
        });
    </script>
</body>
</html>
