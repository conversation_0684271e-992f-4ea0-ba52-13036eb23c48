# Dependencies
node_modules/
*/node_modules/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
server/logs/*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Build directories
build/
dist/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Uploads and user-generated content
server/uploads/candidates/*
server/uploads/idProofs/*
server/uploads/profiles/*
server/uploads/symbols/*
!server/uploads/*/README.md

# Test files and temporary data
test_results/
temp/
*.tmp

# Documentation (auto-generated)
COMPREHENSIVE_SUMMARY.md
PROJECT_ANALYSIS.md
ML_DOCUMENT_SYSTEM_README.md
SAMPLE_DATA.md
SETUP_AND_TESTING_GUIDE.md