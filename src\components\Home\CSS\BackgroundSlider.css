.background-slider {
    width: 100%;
    height: 100vh;
    position: relative;
    /* overflow: hidden; */
}

.sliding-image{
    display: flex;
}

.background-image {
    width: 100%;
    height: 100%;
    /* object-fit: cover; */
    position: absolute;
    top: 0;
    left: 0;
    /* animation: slide 5s linear infinite; */
}

.next {
    animation-delay: 0s;
}

@keyframes slide {
    0% {
        left: 0;
    }

    100% {
        left: -100%;
    }
}

.background-slider .content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
    color: white;
    text-align: center;
  }